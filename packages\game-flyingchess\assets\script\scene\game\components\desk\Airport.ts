import { _decorator, Component, Node, Vec3, v3, Sprite<PERSON>rame, Sprite } from 'cc'
import { BaseComponent } from '@/core/manager/gui/base/BaseComponent'
import { cat } from '@/core/manager'

const { ccclass, property } = _decorator

export interface AirportProps {
    playerPosition: number
    position: Vec3
}

export interface AirportData {
    // 可以添加其他数据
}

@ccclass('Airport')
export class Airport extends BaseComponent<AirportProps, AirportData> {
    @property({ type: [SpriteFrame], tooltip: '玩家飞机场精灵图集' })
    airport_spriteFrames: SpriteFrame[] = []

    @property({ type: Sprite, tooltip: '飞机场精灵组件' })
    airportSprite: Sprite = null!

    private playerPosition: number = 0

    protected override initUI(): void {
        // 设置精灵帧逻辑
        this.setupSpriteFrame()
        this.node.setPosition(this.props.position)
        this.node.setScale(2.82, 2.82, 2.82)
        cat.util.nodeUtils.setNodeAndChildrenLayer(this.node, 'UI_IN_SCENE')
    }

    private setupSpriteFrame(): void {
        if (!this.props) return

        const { playerPosition } = this.props

        const spriteFrames = this.airport_spriteFrames

        // 添加Sprite组件（如果不存在）
        let spriteComponent = this.node.getComponent(Sprite)
        if (!spriteComponent) {
            spriteComponent = this.node.addComponent(Sprite)
        }

        // 根据玩家位置获取对应的精灵帧
        if (spriteFrames[playerPosition]) {
            spriteComponent.spriteFrame = spriteFrames[playerPosition]
        }

        // 保存玩家位置
        this.playerPosition = playerPosition
    }
}
